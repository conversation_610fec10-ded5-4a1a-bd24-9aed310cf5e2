@import "tailwindcss";

@theme {
  /* Your custom colors - now theme-aware */
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-accent: #f26430;     /* Your accent color stays the same */

  /* Your custom fonts */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

html {
  scroll-behavior: smooth;
}

html, body {
  height: 100%;
  height: 100dvh;
}

/* Default theme variables (dark mode) */
:root {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Light mode theme */
:root[data-theme="light"] {
  --color-primary: #ffffff;       /* Light background (was secondary) */
  --color-secondary: #0b0505;     /* Dark text (was primary) */
  --color-background: #ffffff;    /* Light background */
  --color-foreground: #0b0505;    /* Dark text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 255, 234, 214;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Dark mode theme (explicit) */
:root[data-theme="dark"] {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans);
}

.scroll-down-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Custom Scrollbar Styling - USING THEME VARIABLES */

/* Webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* Use theme variables for scrollbar */
::-webkit-scrollbar-thumb {
  background: color-mix(in srgb, var(--color-secondary) 40%, transparent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Firefox - use theme variables */
html {
  scrollbar-width: thin;
  scrollbar-color: color-mix(in srgb, var(--color-secondary) 40%, transparent) transparent;
}

/* Modal scroll prevention without layout shift */
body.modal-open {
  /* Styles applied via JavaScript to prevent layout shift */
}

/* Modal content scrollbar styling */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.modal-content::-webkit-scrollbar-thumb {
  background: color-mix(in srgb, var(--color-secondary) 30%, transparent);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Safari mobile viewport fix */
.h-screen {
  height: 100vh;
  height: 100dvh;
}

.min-h-screen {
  min-height: 100vh;
  min-height: 100dvh;
}

/* Navbar hover effects */
.navbar-item {
  position: relative;
  transition: color 0.3s ease;
}

.navbar-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-item:hover::after {
  width: 100%;
}

.navbar-item:hover {
  color: var(--color-accent);
}

/* Button hover effect - simple fade version with border */
.request-project-btn {
  transition: all 0.1s ease;
}

.request-project-btn:hover {
  background-color: var(--color-accent) !important;
  color: white !important;
}
